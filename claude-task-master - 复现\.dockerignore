# Docker 忽略文件
# 避免不必要的文件被复制到镜像中

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境配置
.env
.env.local
.env.development
.env.test
.env.production

# IDE 配置
.vscode/
.idea/
*.swp
*.swo
*~

# Git
.git/
.gitignore

# 文档
*.md
docs/
README*
CHANGELOG*
LICENSE

# 测试
coverage/
.nyc_output/
test-results/

# 构建产物
dist/
build/
.next/
out/

# 日志
logs/
*.log

# 临时文件
tmp/
temp/
.tmp/

# 操作系统
.DS_Store
Thumbs.db

# Docker 文件
Dockerfile*
docker-compose*
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml

# 数据文件
data/
*.db
*.sqlite

# 缓存
.cache/
.npm/
.yarn/

# 备份文件
*.bak
*.backup
*.old

# 下载依赖目录中的大文件（保留目录结构）
下载依赖/docker-images/*.tar.gz
下载依赖/node-binaries/*.tar.xz
下载依赖/tools/*.tgz

# 但保留这些配置文件
!下载依赖/README.md
!下载依赖/dependencies-manifest.json
!下载依赖/offline-*.sh