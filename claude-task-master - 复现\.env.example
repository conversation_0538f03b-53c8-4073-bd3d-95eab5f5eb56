# API Keys (Required for using in any role i.e. main/research/fallback -- see `task-master models`)
ANTHROPIC_API_KEY=YOUR_ANTHROPIC_KEY_HERE
PERPLEXITY_API_KEY=YOUR_PERPLEXITY_KEY_HERE
OPENAI_API_KEY=YOUR_OPENAI_KEY_HERE
GOOGLE_API_KEY=YOUR_GOOGLE_KEY_HERE
MISTRAL_API_KEY=YOUR_MISTRAL_KEY_HERE
GROQ_API_KEY=YOUR_GROQ_KEY_HERE
OPENROUTER_API_KEY=YOUR_OPENROUTER_KEY_HERE
XAI_API_KEY=YOUR_XAI_KEY_HERE
AZURE_OPENAI_API_KEY=YOUR_AZURE_KEY_HERE
OLLAMA_API_KEY=YOUR_OLLAMA_API_KEY_HERE

# Google Vertex AI Configuration
VERTEX_PROJECT_ID=your-gcp-project-id
VERTEX_LOCATION=us-central1
# Optional: Path to service account credentials JSON file (alternative to API key)
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-credentials.json

# ===================
# Logging Configuration
# ===================
# Log level: error, warn, info, http, verbose, debug, silly
LOG_LEVEL=info

# Enable/disable logging outputs
LOG_CONSOLE=true
LOG_FILE=true
LOG_SILENT=false

# Log directory (relative to project root)
LOG_DIR=logs

# Log rotation settings
LOG_MAX_FILES=30d
LOG_MAX_SIZE=20m

# ===================
# Application Settings
# ===================
NODE_ENV=development
PORT=3000

# Cache configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# ===================
# Security Settings
# ===================
JWT_SECRET=your_jwt_secret_here
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ===================
# Performance Settings
# ===================
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30000
HEALTH_CHECK_INTERVAL=60000
