# Task Master AI 环境配置模板
# 复制此文件为 .env.local 并填入真实的配置值

# ===========================================
# 基本配置
# ===========================================
NODE_ENV=development
DEBUG=false

# 服务端口配置
PORT=3000
MCP_PORT=3001

# ===========================================
# 数据库配置
# ===========================================
# PostgreSQL 数据库连接
DATABASE_URL=postgresql://taskmaster:password@localhost:5432/taskmaster

# Redis 缓存连接
REDIS_URL=redis://localhost:6379

# ===========================================
# AI 提供商 API 密钥配置
# ===========================================
# 请填入真实的 API 密钥，未使用的可以留空或删除

# Anthropic (Claude) - 推荐使用
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# OpenAI (GPT) 
OPENAI_API_KEY=your_openai_api_key_here

# Google AI (Gemini)
GOOGLE_API_KEY=your_google_api_key_here

# Perplexity (推荐用于研究功能)
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# xAI (Grok)
XAI_API_KEY=your_xai_api_key_here

# Groq (快速推理)
GROQ_API_KEY=your_groq_api_key_here

# OpenRouter (多模型访问)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Mistral AI
MISTRAL_API_KEY=your_mistral_api_key_here

# Azure OpenAI
AZURE_OPENAI_API_KEY=your_azure_api_key_here
AZURE_OPENAI_ENDPOINT=your_azure_endpoint_here

# Ollama (本地 LLM)
OLLAMA_API_KEY=your_ollama_api_key_here
OLLAMA_BASE_URL=http://localhost:11434

# ===========================================
# 缓存配置
# ===========================================
# 缓存生存时间（秒）
CACHE_TTL=3600

# 缓存前缀
CACHE_PREFIX=taskmaster:

# ===========================================
# 日志配置
# ===========================================
# 日志级别: error, warn, info, debug
LOG_LEVEL=info

# 日志文件路径
LOG_FILE=./logs/taskmaster.log

# 是否启用控制台日志
LOG_CONSOLE=true

# 日志文件最大大小 (MB)
LOG_MAX_SIZE=50

# 保留的日志文件数量
LOG_MAX_FILES=5

# ===========================================
# 安全配置
# ===========================================
# JWT 密钥（用于 API 认证）
JWT_SECRET=your_jwt_secret_here

# 会话密钥
SESSION_SECRET=your_session_secret_here

# CORS 允许的域名
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# ===========================================
# 任务管理配置
# ===========================================
# 默认任务标签
DEFAULT_TAG=master

# 最大任务数量
MAX_TASKS=1000

# 任务自动保存间隔（秒）
TASK_SAVE_INTERVAL=30

# ===========================================
# MCP 服务器配置
# ===========================================
# MCP 服务器名称
MCP_SERVER_NAME=task-master-ai

# MCP 服务器版本
MCP_SERVER_VERSION=0.21.0

# ===========================================
# 监控配置
# ===========================================
# 是否启用指标收集
METRICS_ENABLED=true

# 指标收集端口
METRICS_PORT=9100

# 健康检查端点
HEALTH_CHECK_PATH=/health

# ===========================================
# 开发配置
# ===========================================
# 是否启用热重载
HOT_RELOAD=true

# 是否启用详细日志
VERBOSE_LOGGING=false

# 调试端口（Node.js Inspector）
DEBUG_PORT=9229

# ===========================================
# 生产配置
# ===========================================
# 工作进程数量（0 = CPU 核心数）
WORKER_PROCESSES=0

# 是否启用集群模式
CLUSTER_MODE=false

# 最大内存使用（MB）
MAX_MEMORY=512

# ===========================================
# 第三方服务配置
# ===========================================
# Sentry 错误追踪（可选）
SENTRY_DSN=your_sentry_dsn_here

# New Relic 性能监控（可选）
NEW_RELIC_LICENSE_KEY=your_newrelic_key_here

# ===========================================
# 功能开关
# ===========================================
# 是否启用研究功能
ENABLE_RESEARCH=true

# 是否启用复杂度分析
ENABLE_COMPLEXITY_ANALYSIS=true

# 是否启用自动任务扩展
ENABLE_AUTO_EXPAND=true

# 是否启用 AI 代理模式
ENABLE_AI_AGENT=true