# Task Master AI Dockerfile
# 多阶段构建，优化镜像大小

# === 构建阶段 ===
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# 复制包管理文件
COPY package*.json ./
COPY .npmrc ./

# 创建本地依赖目录
RUN mkdir -p ./下载依赖/npm-cache

# 安装依赖（使用本地缓存）
RUN npm ci --cache ./下载依赖/npm-cache --prefer-offline --no-audit --no-fund

# 复制源代码
COPY . .

# 构建项目（如果有构建步骤）
RUN npm run prepare || true

# 运行测试（可选）
# RUN npm test

# === 运行时阶段 ===  
FROM node:18-alpine AS runtime

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3000
ENV MCP_PORT=3001

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S taskmaster -u 1001

# 安装运行时依赖
RUN apk add --no-cache \
    dumb-init \
    curl \
    ca-certificates

# 设置工作目录
WORKDIR /app

# 从构建阶段复制文件
COPY --from=builder --chown=taskmaster:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=taskmaster:nodejs /app/package*.json ./
COPY --chown=taskmaster:nodejs . .

# 创建运行时目录
RUN mkdir -p logs tmp data && \
    chown -R taskmaster:nodejs logs tmp data

# 切换到非 root 用户
USER taskmaster

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:$PORT/health || exit 1

# 暴露端口
EXPOSE $PORT $MCP_PORT

# 启动命令
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "index.js"]

# 添加标签
LABEL maintainer="Task Master AI Team"
LABEL version="0.21.0"
LABEL description="Task Master AI - AI-driven development task management"