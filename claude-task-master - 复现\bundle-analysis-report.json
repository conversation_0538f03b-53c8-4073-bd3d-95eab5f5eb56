{"summary": {"totalModules": 345, "totalSize": 2871553, "totalLines": 98126, "externalDependencies": 0, "circularDependencies": 15, "unusedModules": 162}, "largestModules": [{"path": "mcp-server\\server.js", "size": 701, "lines": 37, "totalSize": 1075339}, {"path": "mcp-server\\src\\index.js", "size": 3552, "lines": 143, "totalSize": 1074638}, {"path": "mcp-server\\src\\tools\\index.js", "size": 4113, "lines": 108, "totalSize": 1042015}, {"path": "tests\\unit\\commands.test.js", "size": 14727, "lines": 463, "totalSize": 969266}, {"path": "bin\\task-master.js", "size": 11817, "lines": 378, "totalSize": 966356}, {"path": "test-version-check-full.js", "size": 2746, "lines": 84, "totalSize": 957285}, {"path": "test-version-check.js", "size": 1224, "lines": 36, "totalSize": 955763}, {"path": "scripts\\dev.js", "size": 624, "lines": 24, "totalSize": 955163}, {"path": "scripts\\modules\\commands.js", "size": 148709, "lines": 5012, "totalSize": 954539}, {"path": "mcp-server\\src\\tools\\analyze.js", "size": 4976, "lines": 171, "totalSize": 924068}], "mostDependencies": [{"path": "mcp-server\\src\\tools\\index.js", "dependencies": 35, "externalDeps": 0, "internalDeps": 35}, {"path": "mcp-server\\src\\core\\task-master-core.js", "dependencies": 33, "externalDeps": 0, "internalDeps": 33}, {"path": "scripts\\modules\\task-manager.js", "dependencies": 25, "externalDeps": 0, "internalDeps": 25}, {"path": "scripts\\modules\\commands.js", "dependencies": 21, "externalDeps": 0, "internalDeps": 21}, {"path": "scripts\\modules\\task-manager\\analyze-task-complexity.js", "dependencies": 10, "externalDeps": 0, "internalDeps": 10}, {"path": "scripts\\modules\\task-manager\\expand-task.js", "dependencies": 10, "externalDeps": 0, "internalDeps": 10}, {"path": "scripts\\modules\\task-manager\\update-tasks.js", "dependencies": 10, "externalDeps": 0, "internalDeps": 10}, {"path": "scripts\\init.js", "dependencies": 8, "externalDeps": 0, "internalDeps": 8}, {"path": "scripts\\modules\\task-manager\\add-task.js", "dependencies": 8, "externalDeps": 0, "internalDeps": 8}, {"path": "scripts\\modules\\task-manager\\update-subtask-by-id.js", "dependencies": 8, "externalDeps": 0, "internalDeps": 8}], "circularDependencies": [["scripts\\modules\\utils.js", "scripts\\modules\\config-manager.js", "src\\utils\\path-utils.js", "src\\utils\\logger-utils.js", "scripts\\modules\\utils.js"], ["scripts\\modules\\utils.js", "scripts\\modules\\config-manager.js", "scripts\\modules\\utils.js"], ["scripts\\modules\\ui.js", "scripts\\modules\\task-manager.js", "scripts\\modules\\task-manager\\parse-prd.js", "scripts\\modules\\ui.js"], ["scripts\\modules\\ui.js", "scripts\\modules\\task-manager.js", "scripts\\modules\\task-manager\\update-tasks.js", "scripts\\modules\\ui.js"], ["scripts\\modules\\ui.js", "scripts\\modules\\task-manager.js", "scripts\\modules\\task-manager\\update-tasks.js", "scripts\\modules\\task-manager\\generate-task-files.js", "scripts\\modules\\ui.js"], ["scripts\\modules\\ui.js", "scripts\\modules\\task-manager.js", "scripts\\modules\\task-manager\\update-tasks.js", "scripts\\modules\\task-manager\\generate-task-files.js", "scripts\\modules\\dependency-manager.js", "scripts\\modules\\ui.js"], ["scripts\\modules\\task-manager.js", "scripts\\modules\\task-manager\\update-tasks.js", "scripts\\modules\\task-manager\\generate-task-files.js", "scripts\\modules\\dependency-manager.js", "scripts\\modules\\task-manager.js"], ["scripts\\modules\\ui.js", "scripts\\modules\\task-manager.js", "scripts\\modules\\task-manager\\update-task-by-id.js", "scripts\\modules\\ui.js"], ["scripts\\modules\\ui.js", "scripts\\modules\\task-manager.js", "scripts\\modules\\task-manager\\set-task-status.js", "scripts\\modules\\ui.js"], ["scripts\\modules\\ui.js", "scripts\\modules\\task-manager.js", "scripts\\modules\\task-manager\\list-tasks.js", "scripts\\modules\\ui.js"], ["scripts\\modules\\ui.js", "scripts\\modules\\task-manager.js", "scripts\\modules\\task-manager\\expand-task.js", "scripts\\modules\\ui.js"], ["scripts\\modules\\ui.js", "scripts\\modules\\task-manager.js", "scripts\\modules\\task-manager\\clear-subtasks.js", "scripts\\modules\\ui.js"], ["scripts\\modules\\task-manager.js", "scripts\\modules\\task-manager\\add-subtask.js", "scripts\\modules\\task-manager.js"], ["scripts\\modules\\task-manager.js", "scripts\\modules\\task-manager\\move-task.js", "scripts\\modules\\task-manager.js"], ["scripts\\modules\\ui.js", "scripts\\modules\\task-manager.js", "scripts\\modules\\task-manager\\research.js", "scripts\\modules\\ui.js"]], "unusedExports": [{"module": "bin\\task-master.js", "exports": [{"name": "detectCamelCaseFlags", "type": "named"}], "type": "unused-module"}, {"module": "docs\\scripts\\models-json-to-markdown.js", "exports": [], "type": "unused-module"}, {"module": "jest.config.js", "exports": [{"name": "default", "type": "default"}], "type": "unused-module"}, {"module": "mcp-server\\server.js", "exports": [], "type": "unused-module"}, {"module": "mcp-server\\src\\core\\direct-functions\\create-tag-from-branch.js", "exports": [], "type": "unused-module"}, {"module": "mcp-server\\src\\core\\utils\\env-utils.js", "exports": [], "type": "unused-module"}, {"module": "mcp-server\\src\\core\\__tests__\\context-manager.test.js", "exports": [], "type": "unused-module"}, {"module": "mcp-server\\src\\tools\\get-operation-status.js", "exports": [{"name": "registerGetOperationStatusTool", "type": "named"}], "type": "unused-module"}, {"module": "mcp-test.js", "exports": [], "type": "unused-module"}, {"module": "rollup.config.js", "exports": [{"name": "default", "type": "default"}, {"name": "rollupConfig", "type": "named"}], "type": "unused-module"}, {"module": "scripts\\bundle-analyzer.js", "exports": [{"name": "BundleAnalyzer", "type": "named"}], "type": "unused-module"}, {"module": "scripts\\demo-command-factory.js", "exports": [{"name": "demoCommandFactory", "type": "named"}], "type": "unused-module"}, {"module": "scripts\\demo-error-handling.js", "exports": [{"name": "createDemoApp", "type": "named"}, {"name": "startDemoServer", "type": "named"}], "type": "unused-module"}, {"module": "scripts\\demo-lazy-loading.js", "exports": [], "type": "unused-module"}, {"module": "scripts\\dev.js", "exports": [], "type": "unused-module"}, {"module": "scripts\\fix-import-syntax.js", "exports": [], "type": "unused-module"}, {"module": "scripts\\modules\\commands\\analysis-commands.js", "exports": [{"name": "registerAnalysisCommands", "type": "named"}], "type": "unused-module"}, {"module": "scripts\\modules\\commands\\base-commands.js", "exports": [{"name": "registerBaseCommands", "type": "named"}], "type": "unused-module"}, {"module": "scripts\\modules\\commands\\subtask-commands.js", "exports": [{"name": "registerSubtaskCommands", "type": "named"}], "type": "unused-module"}, {"module": "scripts\\modules\\commands\\tag-commands.js", "exports": [{"name": "registerTagCommands", "type": "named"}], "type": "unused-module"}, {"module": "scripts\\modules\\commands\\task-commands.js", "exports": [{"name": "registerTaskCommands", "type": "named"}], "type": "unused-module"}, {"module": "scripts\\modules\\commands\\update-commands.js", "exports": [{"name": "registerUpdateCommands", "type": "named"}], "type": "unused-module"}, {"module": "scripts\\modules\\commands\\utility-commands.js", "exports": [{"name": "registerUtilityCommands", "type": "named"}], "type": "unused-module"}, {"module": "scripts\\modules\\commands-new.js", "exports": [{"name": "registerCommands", "type": "named"}], "type": "unused-module"}, {"module": "scripts\\modules\\index.js", "exports": [], "type": "unused-module"}, {"module": "scripts\\replace-console-statements.js", "exports": [{"name": "main", "type": "named"}], "type": "unused-module"}, {"module": "scripts\\test-claude-errors.js", "exports": [], "type": "unused-module"}, {"module": "scripts\\test-claude.js", "exports": [], "type": "unused-module"}, {"module": "src\\ai-providers\\anthropic.js", "exports": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named"}], "type": "unused-module"}, {"module": "src\\ai-providers\\azure.js", "exports": [{"name": "AzureProvider", "type": "named"}], "type": "unused-module"}, {"module": "src\\ai-providers\\bedrock.js", "exports": [{"name": "BedrockAIProvider", "type": "named"}], "type": "unused-module"}, {"module": "src\\ai-providers\\claude-code.js", "exports": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named"}], "type": "unused-module"}, {"module": "src\\ai-providers\\custom-sdk\\claude-code\\types.js", "exports": [], "type": "unused-module"}, {"module": "src\\ai-providers\\gemini-cli.js", "exports": [{"name": "GeminiCliProvider", "type": "named"}], "type": "unused-module"}, {"module": "src\\ai-providers\\google-vertex.js", "exports": [{"name": "VertexAIProvider", "type": "named"}], "type": "unused-module"}, {"module": "src\\ai-providers\\google.js", "exports": [{"name": "GoogleAIProvider", "type": "named"}], "type": "unused-module"}, {"module": "src\\ai-providers\\groq.js", "exports": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named"}], "type": "unused-module"}, {"module": "src\\ai-providers\\ollama.js", "exports": [{"name": "Ollama<PERSON><PERSON><PERSON><PERSON>", "type": "named"}], "type": "unused-module"}, {"module": "src\\ai-providers\\openai.js", "exports": [{"name": "OpenAIProvider", "type": "named"}], "type": "unused-module"}, {"module": "src\\ai-providers\\openrouter.js", "exports": [{"name": "OpenRouterAIProvider", "type": "named"}], "type": "unused-module"}, {"module": "src\\ai-providers\\perplexity.js", "exports": [{"name": "PerplexityAIProvider", "type": "named"}], "type": "unused-module"}, {"module": "src\\ai-providers\\xai.js", "exports": [{"name": "XAIProvider", "type": "named"}], "type": "unused-module"}, {"module": "src\\commands\\middleware\\command-validation.js", "exports": [{"name": "createCommandValidation", "type": "named"}, {"name": "predefinedRules", "type": "named"}, {"name": "combineValidations", "type": "named"}, {"name": "conditionalValidation", "type": "named"}, {"name": "requirePermission", "type": "named"}, {"name": "requireEnvironment", "type": "named"}, {"name": "requireFileExists", "type": "named"}, {"name": "requireDependency", "type": "named"}], "type": "unused-module"}, {"module": "src\\middleware\\rate-limiter.js", "exports": [{"name": "rateLimitPresets", "type": "named"}, {"name": "createRateLimiter", "type": "named"}, {"name": "rateLimiters", "type": "named"}, {"name": "AdaptiveRateLimiter", "type": "named"}, {"name": "rateLimitManager", "type": "named"}, {"name": "adaptiveRateLimiter", "type": "named"}, {"name": "default", "type": "default"}], "type": "unused-module"}, {"module": "src\\middleware\\security.js", "exports": [{"name": "securityHeaders", "type": "named"}, {"name": "corsMiddleware", "type": "named"}, {"name": "api<PERSON><PERSON><PERSON><PERSON>", "type": "named"}, {"name": "requestId", "type": "named"}, {"name": "sanitizeInput", "type": "named"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named"}, {"name": "requestSizeLimit", "type": "named"}, {"name": "maliciousRequestDetection", "type": "named"}, {"name": "generateApiKey", "type": "named"}, {"name": "verifyApiKeyHash", "type": "named"}, {"name": "securityMiddleware", "type": "named"}, {"name": "securityPresets", "type": "named"}], "type": "unused-module"}, {"module": "src\\profiles\\amp.js", "exports": [{"name": "ampProfile", "type": "named"}, {"name": "onAddRulesProfile", "type": "named"}, {"name": "onRemoveRulesProfile", "type": "named"}, {"name": "onPostConvertRulesProfile", "type": "named"}], "type": "unused-module"}, {"module": "test-clean-tags.js", "exports": [], "type": "unused-module"}, {"module": "test-config-manager.js", "exports": [], "type": "unused-module"}, {"module": "test-tag-functions.js", "exports": [], "type": "unused-module"}, {"module": "test-version-check-full.js", "exports": [], "type": "unused-module"}, {"module": "test-version-check.js", "exports": [], "type": "unused-module"}, {"module": "tests\\e2e\\parse_llm_output.cjs", "exports": [], "type": "unused-module"}, {"module": "tests\\fixtures\\sample-claude-response.js", "exports": [{"name": "sampleClaudeResponse", "type": "named"}], "type": "unused-module"}, {"module": "tests\\integration\\claude-code-optional.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\cli\\commands.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\manage-gitignore.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\mcp-server\\direct-functions.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\profiles\\amp-init-functionality.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\profiles\\claude-init-functionality.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\profiles\\cline-init-functionality.test.js", "exports": [{"name": "clineProfile", "type": "named"}], "type": "unused-module"}, {"module": "tests\\integration\\profiles\\codex-init-functionality.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\profiles\\cursor-init-functionality.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\profiles\\gemini-init-functionality.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\profiles\\opencode-init-functionality.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\profiles\\roo-files-inclusion.test.js", "exports": [{"name": "ROO_MODES", "type": "named"}], "type": "unused-module"}, {"module": "tests\\integration\\profiles\\roo-init-functionality.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\profiles\\rules-files-inclusion.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\profiles\\trae-init-functionality.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\profiles\\vscode-init-functionality.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\integration\\profiles\\windsurf-init-functionality.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\lazy-loading.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\manual\\prompts\\prompt-test.js", "exports": [{"name": "runComprehensiveTests", "type": "named"}, {"name": "getTestDataForTemplate", "type": "named"}], "type": "unused-module"}, {"module": "tests\\setup.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\ai-providers\\cache-strategies.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\ai-providers\\claude-code.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\ai-providers\\concurrent-manager.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\ai-providers\\custom-sdk\\claude-code\\language-model.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\ai-providers\\gemini-cli.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\ai-providers\\load-balancer.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\ai-providers\\mcp-components.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\ai-services-unified.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\commands\\command-factory.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\commands.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\config-manager.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\config-manager.test.mjs", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\dependency-manager.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\init.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\initialize-project.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\kebab-case-validation.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\manage-gitignore.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\mcp\\tools\\add-task.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\mcp\\tools\\analyze-complexity.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\mcp\\tools\\expand-all.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\mcp\\tools\\get-tasks.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\mcp\\tools\\initialize-project.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\mcp\\tools\\remove-task.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\mcp-providers\\mcp-components.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\mcp-providers\\mcp-provider.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\middleware\\error-handler.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\parse-prd.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\amp-integration.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\claude-integration.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\cline-integration.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\codex-integration.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\cursor-integration.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\gemini-integration.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\kiro-integration.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\mcp-config-validation.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\opencode-integration.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\profile-safety-check.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\roo-integration.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\rule-transformer-cline.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\rule-transformer-cursor.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\rule-transformer-gemini.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\rule-transformer-kiro.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\rule-transformer-opencode.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\rule-transformer-roo.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\rule-transformer-trae.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\rule-transformer-vscode.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\rule-transformer-windsurf.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\rule-transformer-zed.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\rule-transformer.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\selective-profile-removal.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\subdirectory-support.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\trae-integration.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\vscode-integration.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\windsurf-integration.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\profiles\\zed-integration.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\prompt-manager.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\providers\\provider-registry.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\dependency-manager\\fix-dependencies-command.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\add-subtask.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\add-task.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\analyze-task-complexity.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\clear-subtasks.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\complexity-report-tag-isolation.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\expand-all-tasks.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\expand-task.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\find-next-task.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\generate-task-files.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\list-tasks.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\move-task.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\parse-prd.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\remove-subtask.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\remove-task.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\research.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\set-task-status.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\update-single-task-status.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\update-subtask-by-id.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\update-task-by-id.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\task-manager\\update-tasks.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\scripts\\modules\\utils-tag-aware-paths.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\task-finder.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\task-manager\\clear-subtasks.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\task-manager\\move-task.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\task-manager\\tag-boundary.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\task-manager\\tag-management.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\task-master.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\ui.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\utils\\logger.test.js", "exports": [], "type": "unused-module"}, {"module": "tests\\unit\\utils.test.js", "exports": [], "type": "unused-module"}, {"module": "webpack.config.js", "exports": [{"name": "default", "type": "default"}], "type": "unused-module"}], "modulesByCategory": {"Other": {"count": 35, "size": 118462, "lines": 4025}, "Scripts": {"count": 67, "size": 899121, "lines": 30156}, "MCP Server": {"count": 86, "size": 319136, "lines": 11138}, "Utilities": {"count": 19, "size": 241616, "lines": 8047}, "Commands": {"count": 25, "size": 379607, "lines": 13631}, "AI Providers": {"count": 32, "size": 205584, "lines": 7356}, "Middleware": {"count": 6, "size": 77363, "lines": 3158}, "Tests": {"count": 74, "size": 566850, "lines": 18740}, "Prompts": {"count": 1, "size": 63814, "lines": 1875}}, "timestamp": "2025-07-23T16:33:00.123Z", "projectRoot": "D:\\claude镜像\\claude-task-master - 复现"}