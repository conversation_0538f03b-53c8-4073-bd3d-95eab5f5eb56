version: '3.8'

# Task Master AI Docker Compose 配置
# 包含完整的开发和生产环境

services:
  # 主应用服务
  taskmaster:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: taskmaster-app
    restart: unless-stopped
    ports:
      - "3000:3000"  # 主应用端口
      - "3001:3001"  # MCP 服务器端口
    environment:
      - NODE_ENV=production
      - PORT=3000
      - MCP_PORT=3001
      - DATABASE_URL=**********************************************/taskmaster
      - REDIS_URL=redis://redis:6379
      # AI 提供商 API 密钥（从环境变量读取）
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-}
      - PERPLEXITY_API_KEY=${PERPLEXITY_API_KEY:-}
      - XAI_API_KEY=${XAI_API_KEY:-}
      - GROQ_API_KEY=${GROQ_API_KEY:-}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY:-}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY:-}
      - OLLAMA_API_KEY=${OLLAMA_API_KEY:-}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./.taskmaster:/app/.taskmaster
      - ./下载依赖:/app/下载依赖
    depends_on:
      - postgres
      - redis
    networks:
      - taskmaster-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: taskmaster-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=taskmaster
      - POSTGRES_USER=taskmaster
      - POSTGRES_PASSWORD=password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./下载依赖/postgres-backup:/backup
    ports:
      - "5432:5432"
    networks:
      - taskmaster-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U taskmaster -d taskmaster"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: taskmaster-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
      - ./下载依赖/redis-backup:/backup
    ports:
      - "6379:6379"
    networks:
      - taskmaster-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: taskmaster-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/sites:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - taskmaster
    networks:
      - taskmaster-network
    profiles:
      - production

  # 开发环境服务
  taskmaster-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder
    container_name: taskmaster-dev
    restart: "no"
    ports:
      - "3000:3000"
      - "3001:3001"
      - "9229:9229"  # Node.js 调试端口
    environment:
      - NODE_ENV=development
      - DEBUG=1
      - PORT=3000
      - MCP_PORT=3001
      - DATABASE_URL=**********************************************/taskmaster_dev
      - REDIS_URL=redis://redis:6379
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
      - ./下载依赖:/app/下载依赖
    command: ["npm", "run", "dev"]
    depends_on:
      - postgres
      - redis
    networks:
      - taskmaster-network
    profiles:
      - dev

  # 监控服务 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: taskmaster-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
    networks:
      - taskmaster-network
    profiles:
      - monitoring

  # 监控服务 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: taskmaster-grafana
    restart: unless-stopped
    ports:
      - "3002:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - taskmaster-network
    profiles:
      - monitoring

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# 网络配置
networks:
  taskmaster-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16