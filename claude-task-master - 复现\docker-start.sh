#!/bin/bash

# Task Master AI Docker 快速启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Task Master AI Docker 快速启动 ===${NC}"

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker 未安装，请先安装 Docker${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo -e "${RED}❌ Docker Compose 未安装，请先安装 Docker Compose${NC}"
    exit 1
fi

# 获取 docker-compose 命令
DOCKER_COMPOSE="docker-compose"
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
fi

# 检查环境配置
if [ ! -f ".env.local" ]; then
    echo -e "${YELLOW}⚠️  未找到 .env.local 文件，创建默认配置...${NC}"
    cat > .env.local << 'EOF'
# Task Master AI Docker 环境配置
NODE_ENV=production

# 数据库配置
DATABASE_URL=**********************************************/taskmaster
REDIS_URL=redis://redis:6379

# AI 提供商配置（请填入真实的 API 密钥）
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
PERPLEXITY_API_KEY=your_perplexity_api_key_here
XAI_API_KEY=your_xai_api_key_here
GROQ_API_KEY=your_groq_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here
AZURE_OPENAI_API_KEY=your_azure_api_key_here
OLLAMA_API_KEY=your_ollama_api_key_here

# 服务端口
PORT=3000
MCP_PORT=3001
EOF
    echo -e "${YELLOW}⚠️  请编辑 .env.local 文件并填入真实的 API 密钥${NC}"
fi

# 创建必要的目录
echo -e "\n${YELLOW}📁 创建必要的目录...${NC}"
mkdir -p logs data database/init nginx/sites ssl monitoring/grafana/{dashboards,datasources}

# 显示启动选项
echo -e "\n${BLUE}🚀 选择启动模式:${NC}"
echo "1. 生产模式 (taskmaster + postgres + redis)"
echo "2. 开发模式 (taskmaster-dev + postgres + redis + 热重载)"
echo "3. 完整模式 (包含监控: prometheus + grafana)"
echo "4. 仅基础服务 (postgres + redis)"
echo "5. 停止所有服务"
echo "6. 查看服务状态"
echo "7. 查看日志"

read -p "请选择 (1-7): " choice

case $choice in
    1)
        echo -e "\n${YELLOW}🚀 启动生产模式...${NC}"
        $DOCKER_COMPOSE up -d taskmaster postgres redis
        ;;
    2)
        echo -e "\n${YELLOW}🚀 启动开发模式...${NC}"
        $DOCKER_COMPOSE --profile dev up -d taskmaster-dev postgres redis
        ;;
    3)
        echo -e "\n${YELLOW}🚀 启动完整模式（包含监控）...${NC}"
        $DOCKER_COMPOSE --profile monitoring up -d
        ;;
    4)
        echo -e "\n${YELLOW}🚀 启动基础服务...${NC}"
        $DOCKER_COMPOSE up -d postgres redis
        ;;
    5)
        echo -e "\n${YELLOW}🛑 停止所有服务...${NC}"
        $DOCKER_COMPOSE --profile dev --profile monitoring down
        ;;
    6)
        echo -e "\n${BLUE}📊 服务状态:${NC}"
        $DOCKER_COMPOSE ps
        ;;
    7)
        echo -e "\n${BLUE}📋 选择查看日志的服务:${NC}"
        echo "1. Task Master 应用"
        echo "2. PostgreSQL 数据库"
        echo "3. Redis 缓存"
        echo "4. 所有服务"
        read -p "请选择 (1-4): " log_choice
        
        case $log_choice in
            1) $DOCKER_COMPOSE logs -f taskmaster ;;
            2) $DOCKER_COMPOSE logs -f postgres ;;
            3) $DOCKER_COMPOSE logs -f redis ;;
            4) $DOCKER_COMPOSE logs -f ;;
            *) echo -e "${RED}❌ 无效选择${NC}" ;;
        esac
        exit 0
        ;;
    *)
        echo -e "${RED}❌ 无效选择${NC}"
        exit 1
        ;;
esac

# 等待服务启动
echo -e "\n${YELLOW}⏳ 等待服务启动...${NC}"
sleep 10

# 检查服务状态
echo -e "\n${BLUE}📊 服务状态:${NC}"
$DOCKER_COMPOSE ps

# 显示访问信息
echo -e "\n${GREEN}✅ 服务启动完成！${NC}"
echo -e "\n${BLUE}🌐 访问信息:${NC}"

if $DOCKER_COMPOSE ps | grep -q "taskmaster-app.*Up"; then
    echo -e "  • Task Master 应用: ${YELLOW}http://localhost:3000${NC}"
    echo -e "  • MCP 服务器: ${YELLOW}http://localhost:3001${NC}"
elif $DOCKER_COMPOSE ps | grep -q "taskmaster-dev.*Up"; then
    echo -e "  • Task Master 开发: ${YELLOW}http://localhost:3000${NC}"
    echo -e "  • MCP 服务器: ${YELLOW}http://localhost:3001${NC}"
    echo -e "  • 调试端口: ${YELLOW}localhost:9229${NC}"
fi

if $DOCKER_COMPOSE ps | grep -q "postgres.*Up"; then
    echo -e "  • PostgreSQL: ${YELLOW}localhost:5432${NC}"
fi

if $DOCKER_COMPOSE ps | grep -q "redis.*Up"; then
    echo -e "  • Redis: ${YELLOW}localhost:6379${NC}"
fi

if $DOCKER_COMPOSE ps | grep -q "prometheus.*Up"; then
    echo -e "  • Prometheus: ${YELLOW}http://localhost:9090${NC}"
fi

if $DOCKER_COMPOSE ps | grep -q "grafana.*Up"; then
    echo -e "  • Grafana: ${YELLOW}http://localhost:3002${NC} (admin/admin)"
fi

echo -e "\n${BLUE}🔧 管理命令:${NC}"
echo -e "  • 查看日志: ${YELLOW}$DOCKER_COMPOSE logs -f [service]${NC}"
echo -e "  • 停止服务: ${YELLOW}$DOCKER_COMPOSE down${NC}"
echo -e "  • 重启服务: ${YELLOW}$DOCKER_COMPOSE restart [service]${NC}"
echo -e "  • 进入容器: ${YELLOW}$DOCKER_COMPOSE exec taskmaster sh${NC}"

echo -e "\n${GREEN}🎉 Docker 部署完成！${NC}"