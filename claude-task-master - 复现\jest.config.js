export default {
	// Use Node.js environment for testing
	testEnvironment: 'node',

	// Automatically clear mock calls between every test
	clearMocks: true,

	// Indicates whether the coverage information should be collected while executing the test
	collectCoverage: true,

	// The directory where Jest should output its coverage files
	coverageDirectory: 'coverage',

	// Files to collect coverage from
	collectCoverageFrom: [
		'src/**/*.js',
		'scripts/**/*.js',
		'mcp-server/src/**/*.js',
		'!src/**/*.test.js',
		'!src/**/*.spec.js',
		'!**/node_modules/**',
		'!**/coverage/**',
		'!**/tests/**',
		'!**/__tests__/**',
		'!scripts/dev.js',
		'!scripts/init.js'
	],

	// A list of paths to directories that <PERSON><PERSON> should use to search for files in
	roots: ['<rootDir>/tests'],

	// The glob patterns Je<PERSON> uses to detect test files
	testMatch: ['**/__tests__/**/*.js', '**/?(*.)+(spec|test).js'],

	// Transform files
	transform: {},

	// Disable transformations for node_modules
	transformIgnorePatterns: ['/node_modules/'],

	// Set moduleNameMapper for absolute paths
	moduleNameMapper: {
		'^@/(.*)$': '<rootDir>/$1'
	},

	// Setup module aliases
	moduleDirectories: ['node_modules', '<rootDir>'],

	// Configure test coverage thresholds
	coverageThreshold: {
		global: {
			branches: 80,
			functions: 80,
			lines: 80,
			statements: 80
		}
	},

	// Generate coverage report in these formats
	coverageReporters: [
		'text',
		'text-summary',
		'lcov',
		'html',
		'json',
		'cobertura'
	],

	// Coverage path ignore patterns
	coveragePathIgnorePatterns: [
		'/node_modules/',
		'/coverage/',
		'/tests/',
		'/__tests__/',
		'/下载依赖/',
		'.eslintrc.js',
		'jest.config.js',
		'biome.json'
	],

	// Verbose output
	verbose: true,

	// Setup file
	setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],

	// Test timeout (in milliseconds)
	testTimeout: 30000,

	// Number of worker processes
	maxWorkers: '50%',

	// Cache directory
	cacheDirectory: '<rootDir>/.jest-cache',

	// Enable test result caching
	cache: true,

	// Detect open handles that may prevent Jest from exiting cleanly
	detectOpenHandles: true,

	// Force exit after tests complete
	forceExit: false,

	// Indicates whether each individual test should be reported during the run
	verbose: true,

	// Error on deprecated features
	errorOnDeprecated: true,

	// Bail out of the test suite on the first test failure
	bail: false,

	// Run tests in parallel
	runInBand: false,

	// Global test patterns to ignore
	testPathIgnorePatterns: [
		'/node_modules/',
		'/coverage/',
		'/下载依赖/',
		'/.jest-cache/'
	],

	// Watch mode ignore patterns
	watchPathIgnorePatterns: [
		'/node_modules/',
		'/coverage/',
		'/下载依赖/',
		'/.jest-cache/',
		'/logs/',
		'/tmp/'
	],

	// Notify when tests pass/fail
	notify: false,

	// Silent console messages in test output
	silent: false
};
