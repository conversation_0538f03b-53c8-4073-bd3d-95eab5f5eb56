{"name": "task-master-ai", "version": "0.21.0", "description": "A task management system for ambitious AI-driven development that doesn't overwhelm and confuse Cursor.", "main": "index.js", "type": "module", "bin": {"task-master": "bin/task-master.js", "task-master-mcp": "mcp-server/server.js", "task-master-ai": "mcp-server/server.js"}, "workspaces": ["apps/*", "."], "scripts": {"test": "node --experimental-vm-modules node_modules/.bin/jest", "test:fails": "node --experimental-vm-modules node_modules/.bin/jest --onlyFailures", "test:watch": "node --experimental-vm-modules node_modules/.bin/jest --watch", "test:coverage": "node --experimental-vm-modules node_modules/.bin/jest --coverage", "test:coverage-report": "node scripts/generate-coverage-report.js", "test:unit": "node --experimental-vm-modules node_modules/.bin/jest tests/unit", "test:integration": "node --experimental-vm-modules node_modules/.bin/jest tests/integration", "test:ai-providers": "node --experimental-vm-modules node_modules/.bin/jest tests/unit/ai-providers", "test:e2e": "./tests/e2e/run_e2e.sh", "test:e2e-report": "./tests/e2e/run_e2e.sh --analyze-log", "prepare": "chmod +x bin/task-master.js mcp-server/server.js", "changeset": "changeset", "release": "changeset publish", "inspector": "npx @modelcontextprotocol/inspector node mcp-server/server.js", "mcp-server": "node mcp-server/server.js", "format-check": "biome format .", "format": "biome format . --write", "dev": "node scripts/dev.js", "start": "NODE_ENV=production node index.js", "offline:install": "./下载依赖/offline-install.sh", "offline:deploy": "./下载依赖/offline-deploy.sh", "docker:start": "./docker-start.sh", "docker:build": "docker build -t task-master-ai .", "docker:clean": "docker system prune -f", "deps:download": "npm run offline:install", "deps:deploy": "npm run offline:deploy", "health": "curl -f http://localhost:3000/health || echo 'Service not available'", "logs:replace": "node scripts/replace-console-statements.js", "logs:replace-dry": "node scripts/replace-console-statements.js --dry-run", "logs:cleanup": "node -e \"import('./src/utils/logger.js').then(m => m.cleanupLogs())\"", "logs:test": "LOG_LEVEL=debug node -e \"import('./src/utils/logger.js').then(m => { const {log} = m; log.info('Test info'); log.error('Test error'); log.debug('Test debug'); })\"", "demo:errors": "node scripts/demo-error-handling.js", "demo:commands": "node scripts/demo-command-factory.js", "middleware:test": "npm run test:unit -- tests/unit/middleware", "security:test": "node -e \"import('./src/middleware/security.js').then(m => console.log('Security middleware loaded successfully'))\"", "validation:test": "node -e \"import('./src/middleware/validation.js').then(m => console.log('Validation middleware loaded successfully'))\"", "commands:test": "npm run test:unit -- tests/unit/commands/command-factory.test.js", "bundle:analyze": "node scripts/bundle-analyzer.js", "bundle:analyze-json": "node scripts/bundle-analyzer.js > bundle-analysis-report.json", "build:webpack": "webpack --config webpack.config.js", "build:webpack-prod": "NODE_ENV=production webpack --config webpack.config.js", "build:webpack-analyze": "ANALYZE_BUNDLE=true webpack --config webpack.config.js", "build:rollup": "rollup -c rollup.config.js", "build:rollup-prod": "NODE_ENV=production rollup -c rollup.config.js", "build:rollup-analyze": "ANALYZE_BUNDLE=true rollup -c rollup.config.js", "build:all": "npm run build:webpack && npm run build:rollup", "build:prod": "NODE_ENV=production npm run build:all", "lazy:test": "npm run test -- tests/lazy-loading.test.js", "lazy:demo": "node scripts/demo-lazy-loading.js", "import:stats": "node -e \"import('./src/utils/dynamic-import-manager.js').then(m => console.log(m.defaultImportManager.getStats()))\"", "provider:stats": "node -e \"import('./src/ai-providers/lazy-provider-registry.js').then(m => console.log(m.defaultLazyRegistry.getStats()))\"", "command:stats": "node -e \"import('./src/commands/lazy-command-loader.js').then(m => console.log(m.defaultCommandLoader.getStats()))\"", "ts:build": "tsc -p tsconfig.build.json", "ts:watch": "tsc -p tsconfig.build.json --watch", "ts:check": "tsc --noEmit", "ts:clean": "rimraf dist/ts", "ts:migrate": "node scripts/migrate-to-typescript.js", "ts:generate-types": "tsc --declaration --emitDeclarationOnly --outDir dist/types", "prebuild": "npm run ts:clean", "build": "npm run ts:build", "postbuild": "npm run ts:generate-types"}, "keywords": ["claude", "task", "management", "ai", "development", "cursor", "anthropic", "llm", "mcp", "context"], "author": "<PERSON><PERSON>", "license": "MIT WITH Commons-<PERSON><PERSON>", "dependencies": {"@ai-sdk/amazon-bedrock": "^2.2.9", "@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/azure": "^1.3.17", "@ai-sdk/google": "^1.2.13", "@ai-sdk/google-vertex": "^2.2.23", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/mistral": "^1.2.7", "@ai-sdk/openai": "^1.3.20", "@ai-sdk/perplexity": "^1.1.7", "@ai-sdk/xai": "^1.2.15", "@anthropic-ai/sdk": "^0.39.0", "@aws-sdk/credential-providers": "^3.817.0", "@inquirer/search": "^3.0.15", "@openrouter/ai-sdk-provider": "^0.4.5", "ai": "^4.3.10", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "boxen": "^8.0.1", "chalk": "^5.4.1", "cli-highlight": "^2.1.11", "cli-table3": "^0.6.5", "commander": "^11.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "fastmcp": "^3.5.0", "figlet": "^1.8.0", "fuse.js": "^7.1.0", "gpt-tokens": "^1.3.14", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "inquirer": "^12.5.0", "jsonc-parser": "^3.3.1", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.2.0", "ollama-ai-provider": "^1.2.0", "openai": "^4.89.0", "ora": "^8.2.0", "uuid": "^11.1.0", "winston": "3.17.0", "winston-daily-rotate-file": "5.0.0", "zod": "^3.23.8", "zod-to-json-schema": "^3.24.5"}, "optionalDependencies": {"@anthropic-ai/claude-code": "^1.0.25", "@biomejs/cli-linux-x64": "^1.9.4", "ai-sdk-provider-gemini-cli": "^0.0.4"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/eyaltoledano/claude-task-master.git"}, "homepage": "https://github.com/eyaltoledano/claude-task-master#readme", "bugs": {"url": "https://github.com/eyaltoledano/claude-task-master/issues"}, "files": ["scripts/**", "assets/**", ".cursor/**", "README-task-master.md", "index.js", "bin/**", "mcp-server/**", "src/**"], "overrides": {"node-fetch": "^2.6.12", "whatwg-url": "^11.0.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.28.1", "@types/jest": "^29.5.14", "execa": "^8.0.1", "ink": "^5.0.1", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "mock-fs": "^5.5.0", "prettier": "^3.5.3", "react": "^18.3.1", "supertest": "^7.1.0", "tsx": "^4.16.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-bundle-analyzer": "^4.10.1", "webpack-node-externals": "^3.0.0", "terser-webpack-plugin": "^5.3.9", "@babel/core": "^7.23.5", "@babel/preset-env": "^7.23.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "babel-loader": "^9.1.3", "rollup": "^4.9.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-dynamic-import-vars": "^2.1.2", "@rollup/plugin-replace": "^5.0.5", "rollup-plugin-visualizer": "^5.11.0", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/inquirer": "^9.0.7", "@types/figlet": "^1.5.8", "@types/gradient-string": "^1.1.5", "@types/uuid": "^9.0.7", "ts-node": "^10.9.2", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "rimraf": "^5.0.5"}}