/**
 * rollup.config.js
 * Rollup configuration for ES module bundling and tree shaking
 */

import { fileURLToPath } from 'url';
import path from 'path';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import json from '@rollup/plugin-json';
import terser from '@rollup/plugin-terser';
import alias from '@rollup/plugin-alias';
import { visualizer } from 'rollup-plugin-visualizer';
import dynamicImportVars from '@rollup/plugin-dynamic-import-vars';
import replace from '@rollup/plugin-replace';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const isProduction = process.env.NODE_ENV === 'production';
const analyzeBundle = process.env.ANALYZE_BUNDLE === 'true';

// Shared output configuration
const outputConfig = {
	dir: 'dist/rollup',
	format: 'es',
	sourcemap: true,
	entryFileNames: '[name].js',
	chunkFileNames: 'chunks/[name]-[hash].js',
	// Preserve modules structure for better tree shaking
	preserveModules: !isProduction,
	preserveModulesRoot: 'src'
};

// Shared plugins
const basePlugins = [
	// Resolve node modules
	resolve({
		preferBuiltins: true,
		exportConditions: ['node'],
		extensions: ['.js', '.mjs', '.json']
	}),
	
	// Convert CommonJS modules to ES6
	commonjs({
		transformMixedEsModules: true
	}),
	
	// Handle JSON imports
	json(),
	
	// Path aliases
	alias({
		entries: [
			{ find: '@', replacement: path.resolve(__dirname, 'src') },
			{ find: '@utils', replacement: path.resolve(__dirname, 'src/utils') },
			{ find: '@commands', replacement: path.resolve(__dirname, 'src/commands') },
			{ find: '@providers', replacement: path.resolve(__dirname, 'src/ai-providers') },
			{ find: '@middleware', replacement: path.resolve(__dirname, 'src/middleware') }
		]
	}),
	
	// Replace environment variables
	replace({
		'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
		'process.env.BUILD_TIME': JSON.stringify(new Date().toISOString()),
		'process.env.VERSION': JSON.stringify(process.env.npm_package_version || '0.0.0'),
		preventAssignment: true
	}),
	
	// Support dynamic imports with variables
	dynamicImportVars({
		include: ['src/**/*.js'],
		exclude: ['node_modules/**'],
		warnOnError: true
	})
];

// Production-only plugins
const productionPlugins = [
	// Minification
	terser({
		compress: {
			drop_console: false, // Keep logging
			drop_debugger: true,
			pure_funcs: ['console.debug'],
			passes: 2,
			module: true
		},
		mangle: {
			module: true,
			toplevel: true
		},
		format: {
			comments: false
		}
	})
];

// Analysis plugins
const analysisPlugins = analyzeBundle ? [
	visualizer({
		filename: 'bundle-analysis/rollup-stats.html',
		open: true,
		gzipSize: true,
		brotliSize: true,
		template: 'sunburst'
	})
] : [];

// External dependencies (not bundled)
const external = [
	// Node built-ins
	'fs',
	'path',
	'url',
	'crypto',
	'util',
	'stream',
	'os',
	'child_process',
	'events',
	'buffer',
	'querystring',
	'http',
	'https',
	'zlib',
	'assert',
	'tty',
	'net',
	'dns',
	'readline',
	'cluster',
	'module',
	'vm',
	'worker_threads',
	
	// Keep some heavy dependencies external in production
	...(isProduction ? [] : [
		'chalk',
		'winston',
		'commander',
		'inquirer',
		'ora',
		'dotenv'
	])
];

// Configuration array for multiple builds
export default [
	// Main CLI bundle
	{
		input: 'bin/task-master.js',
		output: {
			...outputConfig,
			entryFileNames: 'cli.js'
		},
		external,
		plugins: [
			...basePlugins,
			...(isProduction ? productionPlugins : []),
			...analysisPlugins
		]
	},
	
	// MCP Server bundle
	{
		input: 'mcp-server/server.js',
		output: {
			...outputConfig,
			entryFileNames: 'mcp-server.js'
		},
		external,
		plugins: [
			...basePlugins,
			...(isProduction ? productionPlugins : [])
		]
	},
	
	// Core utilities bundle (used by multiple entry points)
	{
		input: {
			logger: 'src/utils/logger.js',
			'error-handler': 'src/middleware/error-handler.js',
			'dynamic-import': 'src/utils/dynamic-import-manager.js',
			'command-factory': 'src/commands/command-factory.js'
		},
		output: {
			...outputConfig,
			dir: 'dist/rollup/core',
			chunkFileNames: '[name].js'
		},
		external,
		plugins: [
			...basePlugins,
			...(isProduction ? productionPlugins : [])
		]
	},
	
	// Lazy-loaded AI providers bundle
	{
		input: 'src/ai-providers/lazy-provider-registry.js',
		output: {
			...outputConfig,
			dir: 'dist/rollup/providers',
			format: 'es',
			// Enable code splitting for dynamic imports
			inlineDynamicImports: false
		},
		external: [
			...external,
			// Keep individual provider modules external for lazy loading
			/^\.\.\/ai-providers\//
		],
		plugins: [
			...basePlugins,
			...(isProduction ? productionPlugins : [])
		],
		// Mark as having side effects to prevent over-optimization
		treeshake: {
			moduleSideEffects: true,
			propertyReadSideEffects: false
		}
	},
	
	// Lazy-loaded commands bundle
	{
		input: 'src/commands/lazy-command-loader.js',
		output: {
			...outputConfig,
			dir: 'dist/rollup/commands',
			format: 'es',
			inlineDynamicImports: false
		},
		external: [
			...external,
			// Keep command definition modules external for lazy loading
			/^\.\/definitions\//
		],
		plugins: [
			...basePlugins,
			...(isProduction ? productionPlugins : [])
		],
		treeshake: {
			moduleSideEffects: true,
			propertyReadSideEffects: false
		}
	}
];

// Export configuration for programmatic use
export const rollupConfig = {
	outputConfig,
	basePlugins,
	productionPlugins,
	analysisPlugins,
	external
};