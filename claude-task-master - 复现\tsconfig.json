{
  "compilerOptions": {
    // Target modern Node.js
    "target": "ES2022",
    "module": "ES2022",
    "lib": ["ES2022"],
    
    // Module resolution
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    
    // Strict type checking
    "strict": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "alwaysStrict": true,
    
    // Additional checks
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    
    // Emit configuration
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "outDir": "./dist/ts",
    "rootDir": "./",
    "removeComments": false,
    "preserveConstEnums": true,
    
    // Interop constraints
    "forceConsistentCasingInFileNames": true,
    "allowJs": true,
    "checkJs": false,
    
    // Experimental
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    
    // Type acquisition
    "types": ["node", "jest"],
    
    // Path mapping
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@utils/*": ["src/utils/*"],
      "@commands/*": ["src/commands/*"],
      "@providers/*": ["src/ai-providers/*"],
      "@middleware/*": ["src/middleware/*"],
      "@types/*": ["src/types/*"]
    },
    
    // Skip lib check for faster builds
    "skipLibCheck": true
  },
  
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "scripts/**/*.ts",
    "tests/**/*.ts",
    "mcp-server/**/*.ts",
    "bin/**/*.ts"
  ],
  
  "exclude": [
    "node_modules",
    "dist",
    "build",
    "coverage",
    "*.config.js",
    "jest.config.js"
  ],
  
  "ts-node": {
    "esm": true,
    "experimentalSpecifierResolution": "node"
  }
}