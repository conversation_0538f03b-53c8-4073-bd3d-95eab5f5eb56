/**
 * webpack.config.js
 * Webpack configuration for bundle optimization and tree shaking
 */

import path from 'path';
import { fileURLToPath } from 'url';
import webpack from 'webpack';
import TerserPlugin from 'terser-webpack-plugin';
import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer';
import nodeExternals from 'webpack-node-externals';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const isProduction = process.env.NODE_ENV === 'production';
const analyzeBundle = process.env.ANALYZE_BUNDLE === 'true';

export default {
	mode: isProduction ? 'production' : 'development',
	
	// Entry points for different bundles
	entry: {
		// Main CLI entry
		cli: './bin/task-master.js',
		
		// MCP server entry
		'mcp-server': './mcp-server/server.js',
		
		// Core bundle (frequently used modules)
		core: [
			'./src/utils/logger.js',
			'./src/middleware/error-handler.js',
			'./src/commands/command-factory.js',
			'./src/utils/dynamic-import-manager.js'
		],
		
		// AI providers bundle (lazy loaded)
		'ai-providers': './src/ai-providers/lazy-provider-registry.js',
		
		// Commands bundle (lazy loaded)
		commands: './src/commands/lazy-command-loader.js'
	},
	
	output: {
		path: path.resolve(__dirname, 'dist'),
		filename: '[name].bundle.js',
		chunkFilename: '[name].chunk.js',
		library: {
			type: 'module'
		},
		clean: true
	},
	
	experiments: {
		outputModule: true
	},
	
	target: 'node',
	
	externals: [
		// Exclude node_modules from bundle
		nodeExternals({
			// Include these packages in the bundle
			allowlist: [
				'chalk',
				'ora',
				'inquirer',
				'commander',
				'winston',
				'dotenv'
			]
		})
	],
	
	module: {
		rules: [
			{
				test: /\.js$/,
				exclude: /node_modules/,
				use: {
					loader: 'babel-loader',
					options: {
						presets: [
							['@babel/preset-env', {
								targets: {
									node: '16'
								},
								modules: false // Keep ES modules for tree shaking
							}]
						],
						plugins: [
							// Dynamic import support
							'@babel/plugin-syntax-dynamic-import',
							// Optional chaining
							'@babel/plugin-proposal-optional-chaining',
							// Nullish coalescing
							'@babel/plugin-proposal-nullish-coalescing-operator'
						]
					}
				}
			}
		]
	},
	
	resolve: {
		extensions: ['.js', '.mjs', '.json'],
		alias: {
			'@': path.resolve(__dirname, 'src'),
			'@utils': path.resolve(__dirname, 'src/utils'),
			'@commands': path.resolve(__dirname, 'src/commands'),
			'@providers': path.resolve(__dirname, 'src/ai-providers'),
			'@middleware': path.resolve(__dirname, 'src/middleware')
		}
	},
	
	optimization: {
		minimize: isProduction,
		minimizer: [
			new TerserPlugin({
				terserOptions: {
					compress: {
						drop_console: false, // Keep console for logging
						drop_debugger: true,
						pure_funcs: ['console.debug'], // Remove debug logs in production
						passes: 2
					},
					mangle: {
						safari10: true
					},
					format: {
						comments: false
					}
				},
				extractComments: false
			})
		],
		
		// Code splitting configuration
		splitChunks: {
			chunks: 'all',
			cacheGroups: {
				// Vendor libraries
				vendor: {
					test: /[\\/]node_modules[\\/]/,
					name: 'vendor',
					priority: 10,
					reuseExistingChunk: true
				},
				
				// Common utilities
				common: {
					test: /[\\/]src[\\/]utils[\\/]/,
					name: 'common',
					priority: 5,
					minSize: 0
				},
				
				// AI providers (lazy loaded)
				providers: {
					test: /[\\/]src[\\/]ai-providers[\\/]/,
					name: 'providers',
					priority: 3,
					enforce: true
				},
				
				// Commands (lazy loaded)
				commands: {
					test: /[\\/]src[\\/]commands[\\/]/,
					name: 'commands',
					priority: 3,
					enforce: true
				}
			}
		},
		
		// Enable module concatenation (scope hoisting)
		concatenateModules: true,
		
		// Mark unused exports for tree shaking
		usedExports: true,
		
		// Side effects configuration
		sideEffects: false,
		
		// Runtime chunk
		runtimeChunk: {
			name: 'runtime'
		}
	},
	
	plugins: [
		// Define environment variables
		new webpack.DefinePlugin({
			'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
			'process.env.BUILD_TIME': JSON.stringify(new Date().toISOString()),
			'process.env.VERSION': JSON.stringify(process.env.npm_package_version)
		}),
		
		// Progress plugin
		new webpack.ProgressPlugin({
			activeModules: true,
			entries: true,
			modules: true,
			profile: true
		}),
		
		// Bundle analyzer (optional)
		...(analyzeBundle ? [
			new BundleAnalyzerPlugin({
				analyzerMode: 'static',
				reportFilename: '../bundle-report.html',
				openAnalyzer: true,
				generateStatsFile: true,
				statsFilename: '../bundle-stats.json'
			})
		] : []),
		
		// Module concatenation plugin (scope hoisting)
		new webpack.optimize.ModuleConcatenationPlugin(),
		
		// Ignore moment locales (if using moment.js)
		new webpack.IgnorePlugin({
			resourceRegExp: /^\.\/locale$/,
			contextRegExp: /moment$/
		})
	],
	
	// Performance hints
	performance: {
		hints: isProduction ? 'warning' : false,
		maxEntrypointSize: 512000,
		maxAssetSize: 512000
	},
	
	// Source maps
	devtool: isProduction ? 'source-map' : 'eval-source-map',
	
	// Stats configuration
	stats: {
		all: false,
		errors: true,
		warnings: true,
		colors: true,
		modules: false,
		chunks: true,
		chunkGroups: true,
		chunkModules: false,
		chunkOrigins: true,
		depth: false,
		entrypoints: true,
		env: true,
		orphanModules: true,
		providedExports: false,
		reasons: false,
		source: false,
		timings: true,
		usedExports: false,
		version: true,
		assets: true,
		assetsSort: 'size',
		builtAt: true,
		moduleTrace: true,
		errorDetails: true,
		errorStack: true,
		hash: true
	}
};