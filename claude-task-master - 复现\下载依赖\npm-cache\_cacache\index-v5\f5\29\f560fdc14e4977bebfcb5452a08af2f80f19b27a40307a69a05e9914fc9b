
34fbf044a6905cab6ca6b1b00a565ce7030d628e	{"key":"make-fetch-happen:request-cache:https://registry.npmmirror.com/@babel%2fplugin-proposal-private-property-in-object","integrity":"sha512-W3DGwYZx3CM2RP6nSEHhXpHptz13wjK1p8ru68jQHsLJgj8DI0WQvp7g/oCqWU8VlcSoTMiZMYlpuUN0s1hkwA==","time":1753337800193,"size":72035,"metadata":{"time":1753337799780,"url":"https://registry.npmmirror.com/@babel%2fplugin-proposal-private-property-in-object","reqHeaders":{"accept":"application/json"},"resHeaders":{"cache-control":"public, max-age=300","content-encoding":"gzip","content-type":"application/json; charset=utf-8","date":"Thu, 24 Jul 2025 06:16:40 GMT","etag":"W/\"8cf8d1fff69e1bf10a65fc54a60f5005b323ef63\"","vary":"Origin, Accept, Accept-Encoding"},"options":{"compress":true}}}