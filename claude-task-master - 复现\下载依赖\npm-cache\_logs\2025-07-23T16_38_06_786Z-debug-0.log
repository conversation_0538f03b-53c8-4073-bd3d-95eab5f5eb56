0 verbose cli D:\claude\node.exe D:\claude\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.16.0
3 silly config load:file:D:\claude\node_modules\npm\npmrc
4 silly config load:file:D:\claude镜像\claude-task-master - 复现\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc
7 verbose title npm view @anthropic-ai/claude-code@latest version
8 verbose argv "view" "@anthropic-ai/claude-code@latest" "version"
9 verbose logfile logs-max:10 dir:D:\claude镜像\claude-task-master - 复现\下载依赖\npm-cache\_logs\2025-07-23T16_38_06_786Z-
10 verbose logfile D:\claude镜像\claude-task-master - 复现\下载依赖\npm-cache\_logs\2025-07-23T16_38_06_786Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly logfile done cleaning log files
13 http fetch GET 200 https://registry.npmmirror.com/@anthropic-ai%2fclaude-code 661ms (cache revalidated)
14 verbose cwd D:\claude镜像\claude-task-master - 复现
15 verbose os Windows_NT 10.0.22631
16 verbose node v22.16.0
17 verbose npm  v10.9.2
18 verbose exit 0
19 info ok
