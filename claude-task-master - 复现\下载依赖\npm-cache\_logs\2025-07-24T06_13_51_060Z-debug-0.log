0 verbose cli D:\claude\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\bin\npm-cli.js
1 info using npm@11.4.2
2 info using node@v22.16.0
3 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\npmrc
4 silly config load:file:D:\claude镜像\claude-task-master - 复现\.npmrc
5 warn Unknown project config "chromedriver_cdnurl". This will stop working in the next major version of npm.
6 warn Unknown project config "operadriver_cdnurl". This will stop working in the next major version of npm.
7 warn Unknown project config "phantomjs_cdnurl". This will stop working in the next major version of npm.
8 warn Unknown project config "selenium_cdnurl". This will stop working in the next major version of npm.
9 warn Unknown project config "node_inspector_cdnurl". This will stop working in the next major version of npm.
10 warn Unknown project config "python_mirror". This will stop working in the next major version of npm.
11 silly config load:file:C:\Users\<USER>\.npmrc
12 silly config load:file:D:\claude\etc\npmrc
13 verbose title npm run ts:check
14 verbose argv "run" "ts:check"
15 verbose logfile logs-max:10 dir:D:\claude镜像\claude-task-master - 复现\下载依赖\npm-cache\_logs\2025-07-24T06_13_51_060Z-
16 verbose logfile D:\claude镜像\claude-task-master - 复现\下载依赖\npm-cache\_logs\2025-07-24T06_13_51_060Z-debug-0.log
17 silly logfile start cleaning logs, removing 1 files
18 silly logfile done cleaning log files
19 verbose cwd D:\claude镜像\claude-task-master - 复现
20 verbose os Windows_NT 10.0.22631
21 verbose node v22.16.0
22 verbose npm  v11.4.2
23 verbose exit 2
24 verbose code 2
