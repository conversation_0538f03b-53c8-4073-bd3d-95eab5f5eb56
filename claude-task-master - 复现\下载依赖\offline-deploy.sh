#!/bin/bash

# Task Master AI 离线部署脚本
# 此脚本用于在完全离线环境中部署项目

set -e

echo "=== Task Master AI 离线部署脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPS_DIR="$SCRIPT_DIR"

echo -e "${BLUE}项目根目录: $PROJECT_ROOT${NC}"
echo -e "${BLUE}依赖目录: $DEPS_DIR${NC}"

# 检查依赖目录是否存在
if [ ! -d "$DEPS_DIR/npm-cache" ]; then
    echo -e "${RED}❌ 依赖目录不存在，请先运行 ./offline-install.sh${NC}"
    exit 1
fi

# 验证依赖完整性
if [ -f "$DEPS_DIR/checksum.sha256" ]; then
    echo -e "\n${YELLOW}🔍 验证依赖完整性...${NC}"
    cd "$DEPS_DIR"
    if sha256sum -c checksum.sha256 --quiet; then
        echo -e "${GREEN}✅ 依赖完整性验证通过${NC}"
    else
        echo -e "${RED}❌ 依赖完整性验证失败${NC}"
        read -p "是否继续部署？(y/N): " continue_deploy
        if [[ ! "$continue_deploy" =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
fi

cd "$PROJECT_ROOT"

# 1. 安装 Node.js 依赖（离线模式）
echo -e "\n${YELLOW}1. 安装 Node.js 依赖...${NC}"

# 清理现有安装
if [ -d "node_modules" ]; then
    echo "清理现有的 node_modules..."
    rm -rf node_modules
fi

# 使用本地缓存安装依赖
npm install --cache "$DEPS_DIR/npm-cache" --offline --no-audit --no-fund

echo -e "${GREEN}✅ Node.js 依赖安装完成${NC}"

# 2. 加载 Docker 镜像
echo -e "\n${YELLOW}2. 加载 Docker 镜像...${NC}"
if command -v docker &> /dev/null; then
    for image_file in "$DEPS_DIR/docker-images"/*.tar.gz; do
        if [ -f "$image_file" ]; then
            echo "加载镜像: $(basename "$image_file")"
            gunzip -c "$image_file" | docker load
        fi
    done
    echo -e "${GREEN}✅ Docker 镜像加载完成${NC}"
else
    echo -e "${YELLOW}⚠️  Docker 未安装，跳过镜像加载${NC}"
fi

# 3. 安装全局工具（可选）
echo -e "\n${YELLOW}3. 安装全局工具...${NC}"
if [ -d "$DEPS_DIR/tools" ] && [ "$(ls -A "$DEPS_DIR/tools")" ]; then
    for tool_package in "$DEPS_DIR/tools"/*.tgz; do
        if [ -f "$tool_package" ]; then
            echo "安装工具: $(basename "$tool_package")"
            npm install -g "$tool_package" --cache "$DEPS_DIR/npm-cache" || true
        fi
    done
    echo -e "${GREEN}✅ 全局工具安装完成${NC}"
else
    echo -e "${YELLOW}⚠️  未找到全局工具包${NC}"
fi

# 4. 创建环境配置
echo -e "\n${YELLOW}4. 创建环境配置...${NC}"

# 创建本地环境配置文件
if [ ! -f ".env.local" ]; then
    cat > .env.local << 'EOF'
# Task Master AI 本地环境配置
NODE_ENV=development

# 数据库配置
DATABASE_URL=postgresql://taskmaster:password@localhost:5432/taskmaster
REDIS_URL=redis://localhost:6379

# AI 提供商配置（请填入真实的 API 密钥）
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# 服务配置
PORT=3000
MCP_PORT=3001

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/taskmaster.log

# 缓存配置
CACHE_TTL=3600
EOF
    echo -e "${GREEN}✅ 创建了 .env.local 配置文件${NC}"
    echo -e "${YELLOW}⚠️  请编辑 .env.local 文件并填入真实的 API 密钥${NC}"
else
    echo -e "${BLUE}ℹ️  .env.local 文件已存在${NC}"
fi

# 5. 创建日志目录
echo -e "\n${YELLOW}5. 创建运行时目录...${NC}"
mkdir -p logs tmp data

# 6. 运行项目测试
echo -e "\n${YELLOW}6. 运行项目测试...${NC}"
if npm test > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 项目测试通过${NC}"
else
    echo -e "${YELLOW}⚠️  项目测试有警告，但部署继续${NC}"
fi

# 7. 显示部署信息
echo -e "\n${GREEN}🎉 离线部署完成！${NC}"
echo -e "\n${BLUE}📋 部署信息:${NC}"
echo -e "  • 项目根目录: $PROJECT_ROOT"
echo -e "  • 依赖缓存目录: $DEPS_DIR"
echo -e "  • 环境配置文件: .env.local"
echo -e "  • 日志目录: ./logs"

echo -e "\n${BLUE}🚀 启动命令:${NC}"
echo -e "  • 开发模式: ${YELLOW}npm run dev${NC}"
echo -e "  • 生产模式: ${YELLOW}npm start${NC}"
echo -e "  • MCP 服务器: ${YELLOW}npm run mcp-server${NC}"
echo -e "  • Docker 部署: ${YELLOW}docker-compose up -d${NC}"

echo -e "\n${BLUE}🔧 管理命令:${NC}"
echo -e "  • 查看任务: ${YELLOW}npx task-master list${NC}"
echo -e "  • 添加任务: ${YELLOW}npx task-master add-task${NC}"
echo -e "  • 运行测试: ${YELLOW}npm test${NC}"

echo -e "\n${YELLOW}💡 提示:${NC}"
echo -e "  1. 请确保 .env.local 中的 API 密钥配置正确"
echo -e "  2. 如需使用 Docker，请先启动 docker-compose"
echo -e "  3. 首次使用请运行 'npx task-master init' 初始化项目"

echo -e "\n${GREEN}✨ 部署完成，项目已就绪！${NC}"