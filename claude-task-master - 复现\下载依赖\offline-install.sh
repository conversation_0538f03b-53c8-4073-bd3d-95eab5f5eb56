#!/bin/bash

# Task Master AI 离线安装脚本
# 此脚本用于下载所需的所有依赖到本地目录

set -e

echo "=== Task Master AI 离线依赖下载脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPS_DIR="$SCRIPT_DIR"

echo -e "${BLUE}项目根目录: $PROJECT_ROOT${NC}"
echo -e "${BLUE}依赖下载目录: $DEPS_DIR${NC}"

# 创建必要的目录
mkdir -p "$DEPS_DIR"/{npm-cache,yarn-cache,docker-images,node-binaries,tools,resources}

# 1. 下载 Node.js 依赖包
echo -e "\n${YELLOW}1. 下载 NPM 依赖包...${NC}"
cd "$PROJECT_ROOT"

# 清理现有的 node_modules
if [ -d "node_modules" ]; then
    echo "清理现有的 node_modules..."
    rm -rf node_modules
fi

# 使用本地配置下载依赖
npm install --cache "$DEPS_DIR/npm-cache" --prefer-offline

# 2. 下载开发和可选依赖
echo -e "\n${YELLOW}2. 下载开发依赖...${NC}"
npm install --only=dev --cache "$DEPS_DIR/npm-cache" --prefer-offline

# 3. 下载全局工具
echo -e "\n${YELLOW}3. 下载全局工具...${NC}"
GLOBAL_TOOLS=(
    "@biomejs/biome"
    "jest"
    "typescript"
    "ts-node"
    "nodemon"
    "pm2"
)

for tool in "${GLOBAL_TOOLS[@]}"; do
    echo "下载工具: $tool"
    npm pack "$tool" --pack-destination "$DEPS_DIR/tools"
done

# 4. 下载 Docker 基础镜像
echo -e "\n${YELLOW}4. 下载 Docker 镜像...${NC}"
DOCKER_IMAGES=(
    "node:18-alpine"
    "node:20-alpine"
    "redis:7-alpine"
    "postgres:15-alpine"
    "nginx:alpine"
)

for image in "${DOCKER_IMAGES[@]}"; do
    echo "下载镜像: $image"
    docker pull "$image"
    # 保存镜像到本地文件
    image_file=$(echo "$image" | tr '/:' '_')
    docker save "$image" | gzip > "$DEPS_DIR/docker-images/${image_file}.tar.gz"
done

# 5. 下载 Node.js 二进制文件
echo -e "\n${YELLOW}5. 下载 Node.js 二进制文件...${NC}"
NODE_VERSIONS=("18" "20")
PLATFORMS=("linux-x64" "win-x64" "darwin-x64" "darwin-arm64")

for version in "${NODE_VERSIONS[@]}"; do
    for platform in "${PLATFORMS[@]}"; do
        echo "下载 Node.js v$version for $platform"
        curl -L "https://nodejs.org/dist/v${version}.0.0/node-v${version}.0.0-${platform}.tar.xz" \
             -o "$DEPS_DIR/node-binaries/node-v${version}.0.0-${platform}.tar.xz" || true
    done
done

# 6. 创建依赖清单
echo -e "\n${YELLOW}6. 创建依赖清单...${NC}"
cat > "$DEPS_DIR/dependencies-manifest.json" << EOF
{
  "name": "task-master-ai-dependencies",
  "version": "$(node -p "require('./package.json').version")",
  "createdAt": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "platform": "$(uname -s)-$(uname -m)",
  "nodeVersion": "$(node --version)",
  "npmVersion": "$(npm --version)",
  "directories": {
    "npmCache": "./npm-cache",
    "yarnCache": "./yarn-cache",
    "dockerImages": "./docker-images",
    "nodeBinaries": "./node-binaries",
    "tools": "./tools",
    "resources": "./resources"
  },
  "dockerImages": $(printf '%s\n' "${DOCKER_IMAGES[@]}" | jq -R . | jq -s .),
  "globalTools": $(printf '%s\n' "${GLOBAL_TOOLS[@]}" | jq -R . | jq -s .),
  "nodeVersions": $(printf '%s\n' "${NODE_VERSIONS[@]}" | jq -R . | jq -s .)
}
EOF

# 7. 创建完整性校验文件
echo -e "\n${YELLOW}7. 创建完整性校验...${NC}"
find "$DEPS_DIR" -type f -name "*.tgz" -o -name "*.tar.gz" -o -name "*.tar.xz" | \
    xargs sha256sum > "$DEPS_DIR/checksum.sha256"

# 8. 创建使用说明
cat > "$DEPS_DIR/README.md" << 'EOF'
# Task Master AI 离线依赖包

本目录包含了 Task Master AI 项目运行所需的所有依赖包和工具。

## 目录结构

- `npm-cache/` - NPM 包缓存
- `yarn-cache/` - Yarn 包缓存  
- `docker-images/` - Docker 镜像文件
- `node-binaries/` - Node.js 二进制文件
- `tools/` - 全局工具包
- `resources/` - 其他资源文件

## 离线安装步骤

1. 确保项目根目录的 `.npmrc` 和 `.yarnrc` 配置正确
2. 运行离线安装：`./offline-deploy.sh`
3. 启动 Docker 服务：`docker-compose up -d`

## 依赖更新

重新运行 `./offline-install.sh` 可更新所有依赖到最新版本。

## 完整性验证

运行 `sha256sum -c checksum.sha256` 验证文件完整性。
EOF

echo -e "\n${GREEN}✅ 离线依赖下载完成！${NC}"
echo -e "${GREEN}📁 依赖文件保存在: $DEPS_DIR${NC}"
echo -e "${GREEN}📋 依赖清单: $DEPS_DIR/dependencies-manifest.json${NC}"
echo -e "${GREEN}🔒 完整性校验: $DEPS_DIR/checksum.sha256${NC}"

# 显示目录大小统计
echo -e "\n${BLUE}📊 依赖目录大小统计:${NC}"
du -sh "$DEPS_DIR"/* 2>/dev/null | sort -hr

echo -e "\n${YELLOW}💡 下一步: 运行 ./offline-deploy.sh 进行离线部署${NC}"