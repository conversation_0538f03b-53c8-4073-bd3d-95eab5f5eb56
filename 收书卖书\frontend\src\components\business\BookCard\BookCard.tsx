import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { StarFilled, HeartOutlined, HeartFilled } from '@ant-design/icons';
import { theme } from '../../../styles/theme';
import { Card } from '../../../styles/globalStyles';
import Button from '../../ui/Button';

// 图书卡片容器
const BookCardContainer = styled(Card)`
  width: 100%;
  max-width: 280px;
  transition: all ${theme.animation.duration.base} ${theme.animation.easing.easeInOut};
  cursor: pointer;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: ${theme.boxShadow.lg};
  }
`;

// 图书封面容器
const BookImageContainer = styled.div`
  position: relative;
  width: 100%;
  height: 200px;
  margin-bottom: ${theme.spacing.md};
  border-radius: ${theme.borderRadius.base};
  overflow: hidden;
  background-color: ${theme.colors.gray[100]};
`;

// 图书封面图片
const BookImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform ${theme.animation.duration.base} ${theme.animation.easing.easeInOut};
  
  ${BookCardContainer}:hover & {
    transform: scale(1.05);
  }
`;

// 收藏按钮
const FavoriteButton = styled.button`
  position: absolute;
  top: ${theme.spacing.sm};
  right: ${theme.spacing.sm};
  width: 32px;
  height: 32px;
  border-radius: ${theme.borderRadius.full};
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all ${theme.animation.duration.fast} ${theme.animation.easing.easeInOut};
  
  &:hover {
    background-color: ${theme.colors.white};
    transform: scale(1.1);
  }
  
  .anticon {
    font-size: 16px;
    color: ${props => props.className?.includes('favorited') ? theme.colors.error : theme.colors.gray[500]};
  }
`;

// 图书信息区域
const BookInfo = styled.div`
  padding: 0;
`;

// 图书标题
const BookTitle = styled.h3`
  font-size: ${theme.typography.fontSize.base};
  font-weight: ${theme.typography.fontWeight.semibold};
  color: ${theme.colors.textPrimary};
  margin-bottom: ${theme.spacing.xs};
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

// 图书作者
const BookAuthor = styled.p`
  font-size: ${theme.typography.fontSize.sm};
  color: ${theme.colors.textSecondary};
  margin-bottom: ${theme.spacing.sm};
`;

// 评分区域
const RatingContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
  margin-bottom: ${theme.spacing.sm};
`;

const Rating = styled.span`
  font-size: ${theme.typography.fontSize.sm};
  color: ${theme.colors.textSecondary};
`;

// 价格区域
const PriceContainer = styled.div`
  display: flex;
  align-items: baseline;
  gap: ${theme.spacing.sm};
  margin-bottom: ${theme.spacing.md};
`;

const CurrentPrice = styled.span`
  font-size: ${theme.typography.fontSize.lg};
  font-weight: ${theme.typography.fontWeight.bold};
  color: ${theme.colors.error};
`;

const OriginalPrice = styled.span`
  font-size: ${theme.typography.fontSize.sm};
  color: ${theme.colors.textTertiary};
  text-decoration: line-through;
`;

const Discount = styled.span`
  font-size: ${theme.typography.fontSize.xs};
  color: ${theme.colors.success};
  background-color: ${theme.colors.success};
  color: ${theme.colors.white};
  padding: 2px 6px;
  border-radius: ${theme.borderRadius.sm};
`;

// 图书状态标签
const StatusTag = styled.span<{ status: 'new' | 'good' | 'fair' }>`
  position: absolute;
  top: ${theme.spacing.sm};
  left: ${theme.spacing.sm};
  padding: 4px 8px;
  border-radius: ${theme.borderRadius.sm};
  font-size: ${theme.typography.fontSize.xs};
  font-weight: ${theme.typography.fontWeight.medium};
  
  ${props => {
    switch (props.status) {
      case 'new':
        return `
          background-color: ${theme.colors.success};
          color: ${theme.colors.white};
        `;
      case 'good':
        return `
          background-color: ${theme.colors.warning};
          color: ${theme.colors.white};
        `;
      case 'fair':
        return `
          background-color: ${theme.colors.gray[500]};
          color: ${theme.colors.white};
        `;
      default:
        return '';
    }
  }}
`;

// 操作按钮区域
const ActionButtons = styled.div`
  display: flex;
  gap: ${theme.spacing.sm};
`;

// 图书数据接口
export interface BookData {
  id: string;
  title: string;
  author: string;
  image: string;
  currentPrice: number;
  originalPrice?: number;
  rating?: number;
  reviewCount?: number;
  condition: 'new' | 'good' | 'fair';
  isFavorited?: boolean;
}

// BookCard组件属性接口
interface BookCardProps {
  book: BookData;
  onFavoriteClick?: (bookId: string) => void;
  onAddToCart?: (bookId: string) => void;
  showActions?: boolean;
}

// 状态标签文本映射
const statusLabels = {
  new: '全新',
  good: '九成新',
  fair: '八成新'
};

// BookCard组件
export const BookCard: React.FC<BookCardProps> = ({
  book,
  onFavoriteClick,
  onAddToCart,
  showActions = true
}) => {
  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onFavoriteClick?.(book.id);
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onAddToCart?.(book.id);
  };

  const discount = book.originalPrice 
    ? Math.round((1 - book.currentPrice / book.originalPrice) * 100)
    : 0;

  return (
    <Link to={`/books/${book.id}`} style={{ textDecoration: 'none' }}>
      <BookCardContainer shadow>
        <BookImageContainer>
          <BookImage 
            src={book.image} 
            alt={book.title}
            onError={(e) => {
              e.currentTarget.src = '/placeholder-book.jpg';
            }}
          />
          <StatusTag status={book.condition}>
            {statusLabels[book.condition]}
          </StatusTag>
          <FavoriteButton 
            onClick={handleFavoriteClick}
            className={book.isFavorited ? 'favorited' : ''}
          >
            {book.isFavorited ? <HeartFilled /> : <HeartOutlined />}
          </FavoriteButton>
        </BookImageContainer>

        <BookInfo>
          <BookTitle>{book.title}</BookTitle>
          <BookAuthor>作者：{book.author}</BookAuthor>
          
          {book.rating && (
            <RatingContainer>
              {[...Array(5)].map((_, index) => (
                <StarFilled
                  key={index}
                  style={{
                    color: index < Math.floor(book.rating!) 
                      ? theme.colors.warning 
                      : theme.colors.gray[300],
                    fontSize: '14px'
                  }}
                />
              ))}
              <Rating>{book.rating} ({book.reviewCount || 0}条评价)</Rating>
            </RatingContainer>
          )}

          <PriceContainer>
            <CurrentPrice>￥{book.currentPrice.toFixed(2)}</CurrentPrice>
            {book.originalPrice && (
              <>
                <OriginalPrice>￥{book.originalPrice.toFixed(2)}</OriginalPrice>
                {discount > 0 && <Discount>省￥{(book.originalPrice - book.currentPrice).toFixed(2)}</Discount>}
              </>
            )}
          </PriceContainer>

          {showActions && (
            <ActionButtons>
              <Button 
                variant="primary" 
                size="sm" 
                fullWidth
                onClick={handleAddToCart}
              >
                加入购物车
              </Button>
            </ActionButtons>
          )}
        </BookInfo>
      </BookCardContainer>
    </Link>
  );
};

export default BookCard;
