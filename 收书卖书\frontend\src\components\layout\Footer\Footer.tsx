import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { 
  WechatOutlined, 
  QqOutlined, 
  PhoneOutlined, 
  MailOutlined,
  BookOutlined
} from '@ant-design/icons';
import { theme } from '../../../styles/theme';
import { Container, Flex } from '../../../styles/globalStyles';

// Footer容器
const FooterContainer = styled.footer`
  background-color: ${theme.colors.gray[800]};
  color: ${theme.colors.white};
  margin-top: auto;
`;

// Footer主要内容区域
const FooterContent = styled.div`
  padding: ${theme.spacing.xl} 0;
`;

// Footer底部版权区域
const FooterBottom = styled.div`
  border-top: 1px solid ${theme.colors.gray[700]};
  padding: ${theme.spacing.md} 0;
  text-align: center;
  color: ${theme.colors.gray[400]};
  font-size: ${theme.typography.fontSize.sm};
`;

// Footer栏目
const FooterSection = styled.div`
  margin-bottom: ${theme.spacing.lg};
  
  ${theme.mediaQueries.md} {
    margin-bottom: 0;
  }
`;

// Footer标题
const FooterTitle = styled.h3`
  font-size: ${theme.typography.fontSize.lg};
  font-weight: ${theme.typography.fontWeight.semibold};
  margin-bottom: ${theme.spacing.md};
  color: ${theme.colors.white};
`;

// Footer链接列表
const FooterLinkList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const FooterLinkItem = styled.li`
  margin-bottom: ${theme.spacing.sm};
`;

const FooterLink = styled(Link)`
  color: ${theme.colors.gray[300]};
  text-decoration: none;
  transition: color ${theme.animation.duration.fast} ${theme.animation.easing.easeInOut};
  
  &:hover {
    color: ${theme.colors.white};
  }
`;

// 联系方式项目
const ContactItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
  margin-bottom: ${theme.spacing.sm};
  color: ${theme.colors.gray[300]};
  
  .anticon {
    font-size: ${theme.typography.fontSize.lg};
    color: ${theme.colors.primary};
  }
`;

// Logo区域
const FooterLogo = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
  margin-bottom: ${theme.spacing.md};
  
  .anticon {
    font-size: ${theme.typography.fontSize['2xl']};
    color: ${theme.colors.primary};
  }
`;

const LogoText = styled.div`
  font-size: ${theme.typography.fontSize.xl};
  font-weight: ${theme.typography.fontWeight.bold};
  color: ${theme.colors.white};
`;

// 描述文本
const Description = styled.p`
  color: ${theme.colors.gray[400]};
  line-height: 1.6;
  margin-bottom: ${theme.spacing.md};
`;

// 网格布局容器
const FooterGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${theme.spacing.lg};
  
  ${theme.mediaQueries.sm} {
    grid-template-columns: repeat(2, 1fr);
  }
  
  ${theme.mediaQueries.md} {
    grid-template-columns: repeat(4, 1fr);
  }
`;

// Footer组件
export const Footer: React.FC = () => {
  return (
    <FooterContainer>
      <Container>
        <FooterContent>
          <FooterGrid>
            {/* 关于我们 */}
            <FooterSection>
              <FooterLogo>
                <BookOutlined />
                <LogoText>旧书买卖</LogoText>
              </FooterLogo>
              <Description>
                专为大学生打造的二手书买卖平台，让知识传递更有价值，让学习成本更低。
              </Description>
            </FooterSection>

            {/* 快速链接 */}
            <FooterSection>
              <FooterTitle>快速链接</FooterTitle>
              <FooterLinkList>
                <FooterLinkItem>
                  <FooterLink to="/books">图书分类</FooterLink>
                </FooterLinkItem>
                <FooterLinkItem>
                  <FooterLink to="/about">关于我们</FooterLink>
                </FooterLinkItem>
                <FooterLinkItem>
                  <FooterLink to="/help">帮助中心</FooterLink>
                </FooterLinkItem>
                <FooterLinkItem>
                  <FooterLink to="/terms">服务条款</FooterLink>
                </FooterLinkItem>
                <FooterLinkItem>
                  <FooterLink to="/privacy">隐私政策</FooterLink>
                </FooterLinkItem>
              </FooterLinkList>
            </FooterSection>

            {/* 用户服务 */}
            <FooterSection>
              <FooterTitle>用户服务</FooterTitle>
              <FooterLinkList>
                <FooterLinkItem>
                  <FooterLink to="/profile/orders">我的订单</FooterLink>
                </FooterLinkItem>
                <FooterLinkItem>
                  <FooterLink to="/profile/favorites">我的收藏</FooterLink>
                </FooterLinkItem>
                <FooterLinkItem>
                  <FooterLink to="/profile/addresses">收货地址</FooterLink>
                </FooterLinkItem>
                <FooterLinkItem>
                  <FooterLink to="/return-policy">退货政策</FooterLink>
                </FooterLinkItem>
                <FooterLinkItem>
                  <FooterLink to="/faq">常见问题</FooterLink>
                </FooterLinkItem>
              </FooterLinkList>
            </FooterSection>

            {/* 联系我们 */}
            <FooterSection>
              <FooterTitle>联系我们</FooterTitle>
              <ContactItem>
                <PhoneOutlined />
                <span>************</span>
              </ContactItem>
              <ContactItem>
                <MailOutlined />
                <span><EMAIL></span>
              </ContactItem>
              <ContactItem>
                <WechatOutlined />
                <span>微信客服</span>
              </ContactItem>
              <ContactItem>
                <QqOutlined />
                <span>QQ客服</span>
              </ContactItem>
            </FooterSection>
          </FooterGrid>
        </FooterContent>

        <FooterBottom>
          <p>&copy; 2024 旧书买卖平台. 保留所有权利.</p>
        </FooterBottom>
      </Container>
    </FooterContainer>
  );
};

export default Footer;
