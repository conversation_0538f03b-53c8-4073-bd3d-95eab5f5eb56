import React from 'react';
import styled from 'styled-components';
import { Outlet } from 'react-router-dom';
import Header from '../Header';
import Footer from '../Footer';
import { theme } from '../../../styles/theme';

// 主布局容器
const LayoutContainer = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`;

// 主内容区域
const MainContent = styled.main`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

// 内容包装器
const ContentWrapper = styled.div`
  flex: 1;
  padding: ${theme.spacing.lg} 0;
  
  ${theme.mediaQueries.md} {
    padding: ${theme.spacing.xl} 0;
  }
`;

// Layout组件属性接口
interface LayoutProps {
  children?: React.ReactNode;
}

// Layout组件
export const Layout: React.FC<LayoutProps> = ({ children }) => {
  // 这里可以添加全局状态管理，比如用户登录状态、购物车状态等
  const cartItemCount = 3; // 示例数据
  const isLoggedIn = false; // 示例数据
  const userName = '张同学'; // 示例数据

  const handleSearch = (query: string) => {
    console.log('搜索:', query);
    // 这里实现搜索逻辑
  };

  const handleCartClick = () => {
    console.log('点击购物车');
    // 这里实现购物车点击逻辑
  };

  const handleLoginClick = () => {
    console.log('点击登录');
    // 这里实现登录点击逻辑
  };

  const handleLogoutClick = () => {
    console.log('点击退出');
    // 这里实现退出登录逻辑
  };

  return (
    <LayoutContainer>
      <Header
        cartItemCount={cartItemCount}
        isLoggedIn={isLoggedIn}
        userName={userName}
        onSearch={handleSearch}
        onCartClick={handleCartClick}
        onLoginClick={handleLoginClick}
        onLogoutClick={handleLogoutClick}
      />
      
      <MainContent>
        <ContentWrapper>
          {children || <Outlet />}
        </ContentWrapper>
      </MainContent>
      
      <Footer />
    </LayoutContainer>
  );
};

export default Layout;
