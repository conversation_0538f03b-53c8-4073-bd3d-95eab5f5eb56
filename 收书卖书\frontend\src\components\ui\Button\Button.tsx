import React from 'react';
import styled, { css } from 'styled-components';
import { theme } from '../../../styles/theme';

// Button组件的属性接口
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'text' | 'danger';
  size?: 'sm' | 'base' | 'lg';
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  children: React.ReactNode;
}

// 按钮变体样式
const buttonVariants = {
  primary: css`
    background-color: ${theme.colors.primary};
    color: ${theme.colors.white};
    border: 1px solid ${theme.colors.primary};

    &:hover:not(:disabled) {
      background-color: ${theme.colors.primaryLight};
      border-color: ${theme.colors.primaryLight};
    }

    &:active:not(:disabled) {
      background-color: ${theme.colors.primaryDark};
      border-color: ${theme.colors.primaryDark};
    }
  `,
  
  secondary: css`
    background-color: ${theme.colors.gray[100]};
    color: ${theme.colors.textPrimary};
    border: 1px solid ${theme.colors.border};

    &:hover:not(:disabled) {
      background-color: ${theme.colors.gray[200]};
      border-color: ${theme.colors.gray[300]};
    }

    &:active:not(:disabled) {
      background-color: ${theme.colors.gray[300]};
    }
  `,
  
  outline: css`
    background-color: transparent;
    color: ${theme.colors.primary};
    border: 1px solid ${theme.colors.primary};

    &:hover:not(:disabled) {
      background-color: ${theme.colors.primary};
      color: ${theme.colors.white};
    }

    &:active:not(:disabled) {
      background-color: ${theme.colors.primaryDark};
      border-color: ${theme.colors.primaryDark};
    }
  `,
  
  text: css`
    background-color: transparent;
    color: ${theme.colors.primary};
    border: 1px solid transparent;

    &:hover:not(:disabled) {
      background-color: ${theme.colors.gray[100]};
    }

    &:active:not(:disabled) {
      background-color: ${theme.colors.gray[200]};
    }
  `,
  
  danger: css`
    background-color: ${theme.colors.error};
    color: ${theme.colors.white};
    border: 1px solid ${theme.colors.error};

    &:hover:not(:disabled) {
      background-color: #ff7875;
      border-color: #ff7875;
    }

    &:active:not(:disabled) {
      background-color: #d9363e;
      border-color: #d9363e;
    }
  `
};

// 按钮尺寸样式
const buttonSizes = {
  sm: css`
    height: ${theme.sizes.button.sm.height};
    padding: ${theme.sizes.button.sm.padding};
    font-size: ${theme.sizes.button.sm.fontSize};
    border-radius: ${theme.borderRadius.sm};
  `,
  
  base: css`
    height: ${theme.sizes.button.base.height};
    padding: ${theme.sizes.button.base.padding};
    font-size: ${theme.sizes.button.base.fontSize};
    border-radius: ${theme.borderRadius.base};
  `,
  
  lg: css`
    height: ${theme.sizes.button.lg.height};
    padding: ${theme.sizes.button.lg.padding};
    font-size: ${theme.sizes.button.lg.fontSize};
    border-radius: ${theme.borderRadius.base};
  `
};

// 样式化的按钮组件
const StyledButton = styled.button<ButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${theme.spacing.sm};
  font-family: inherit;
  font-weight: ${theme.typography.fontWeight.medium};
  line-height: 1;
  cursor: pointer;
  transition: all ${theme.animation.duration.fast} ${theme.animation.easing.easeInOut};
  user-select: none;
  white-space: nowrap;
  
  ${props => buttonVariants[props.variant || 'primary']}
  ${props => buttonSizes[props.size || 'base']}
  
  ${props => props.fullWidth && css`
    width: 100%;
  `}
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  &:focus-visible {
    outline: 2px solid ${theme.colors.primary};
    outline-offset: 2px;
  }
  
  &:active:not(:disabled) {
    transform: scale(0.98);
  }
`;

// 加载动画组件
const LoadingSpinner = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

// Button组件
export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'base',
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  disabled,
  children,
  ...props
}) => {
  const isDisabled = disabled || loading;
  
  return (
    <StyledButton
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      disabled={isDisabled}
      {...props}
    >
      {loading && <LoadingSpinner />}
      {!loading && icon && iconPosition === 'left' && icon}
      {children}
      {!loading && icon && iconPosition === 'right' && icon}
    </StyledButton>
  );
};

export default Button;
